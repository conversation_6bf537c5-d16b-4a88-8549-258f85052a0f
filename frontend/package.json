{"name": "frontend", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build:copy": "cp -r dist/* ../public/ || xcopy dist\\* ..\\public\\ /E /Y", "start": "serve -s build -l 10000", "lint": "eslint .", "preview": "vite preview", "proxy": "node proxy-server.js"}, "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "express-http-proxy": "^2.1.1", "http-proxy-middleware": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "papaparse": "^5.5.2", "react": "^19.1.0", "react-dom": "^19.1.0", "xlsx": "^0.18.5", "vite": "^6.3.5", "@vitejs/plugin-react": "^4.4.1", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "react-router-dom": "^7.5.3", "axios": "^1.9.0", "@heroicons/react": "^2.2.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "serve": "^14.2.4", "terser": "^5.39.0"}}